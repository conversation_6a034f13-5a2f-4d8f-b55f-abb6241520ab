# AI CLI Tool Configuration

# LLM Settings
llm:
  default_provider: "ollama"  # Options: "ollama", "deepseek"
  
  # Ollama Configuration
  ollama:
    server_url: "http://localhost:11434"
    default_model: "llama2"
    timeout: 30
  
  # Deepseek Configuration  
  deepseek:
    api_key: ""  # Set via environment variable DEEPSEEK_API_KEY
    model: "deepseek-chat, deepseek-reasoner"
    base_url: "https://api.deepseek.com/v1"
    timeout: 30

# Tool Settings
tools:
  shell:
    confirmation_required: true
    allow_sudo: true
    timeout: 60
  
  file_operations:
    confirmation_for_destructive: true
    max_file_size_mb: 10
    
  web_search:
    default_results: 5
    timeout: 15

# Context Settings
context:
  auto_gather: true
  max_files: 20
  max_file_size_kb: 100
  exclude_patterns:
    - "*.pyc"
    - "__pycache__"
    - ".git"
    - "node_modules"
    - "*.log"
  include_git_status: true

# Session Settings
sessions:
  auto_save: true
  max_history_items: 100
  storage_path: "~/.ai_cli_tool/sessions"

# UI Settings
ui:
  show_animations: true
  animation_speed: 0.1
  show_status_bar: true
  syntax_highlighting: true
  diff_context_lines: 3

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file_path: "~/.ai_cli_tool/logs/app.log"
  max_file_size_mb: 10
  backup_count: 5

# Security
security:
  command_confirmation: true
  restricted_commands:
    - "rm -rf"
    - "sudo rm"
    - "format"
    - "del /f /s /q"
